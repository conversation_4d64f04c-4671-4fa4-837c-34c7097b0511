import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { markdownToHtml } from '@/lib/content-converter';
import { cn } from '@/lib/utils';
import { contentSecurity, validateContentSecurity } from '@/lib/content-security';
import { Copy, Check, ExternalLink, X } from 'lucide-react';
import { useIsomorphicLayoutEffect } from '@/hooks/use-isomorphic-layout-effect';
import { processMarkdownContent } from '@/lib/markdown-processor';

// Import Prism for syntax highlighting
import Prism from 'prismjs';
// Import basic languages
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-markup';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-java';
import 'prismjs/components/prism-c';
import 'prismjs/components/prism-cpp';
import 'prismjs/components/prism-csharp';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-yaml';
import 'prismjs/components/prism-markdown';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-php';
import 'prismjs/components/prism-ruby';
import 'prismjs/components/prism-go';
import 'prismjs/components/prism-rust';
import 'prismjs/components/prism-swift';
import 'prismjs/components/prism-kotlin';
import 'prismjs/plugins/line-numbers/prism-line-numbers';
import 'prismjs/plugins/toolbar/prism-toolbar';
import 'prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard';
import 'prismjs/plugins/line-highlight/prism-line-highlight';

interface MarkdownPreviewProps {
  content: string;
  className?: string;
  allowHtml?: boolean;
  securityLevel?: 'basic' | 'extended' | 'full';
}

export function MarkdownPreview({
  content,
  className,
  allowHtml = false,
  securityLevel = 'extended'
}: MarkdownPreviewProps) {
  const previewRef = useRef<HTMLDivElement>(null);
  const [zoomedImage, setZoomedImage] = useState<string | null>(null);
  const [isProcessed, setIsProcessed] = useState(false);
  const processingTimeoutRef = useRef<number | null>(null);

  // Optimized content processing with proper memoization and error handling
  const renderedContent = useMemo(() => {
    if (!content) return '';

    try {
      // Validate content security first
      const validation = validateContentSecurity(content);
      if (!validation.valid) {
        console.warn('Content security validation failed:', validation.errors);
      }

      // Process image URLs efficiently
      let processedContent = content;

      // Handle data URLs for images
      processedContent = processedContent.replace(
        /!\[(.*?)\]\((data:image\/[^)]+)\)/g,
        (match, alt, url) => `![${alt}](${url})`
      );

      // Add support for image dimensions
      processedContent = processedContent.replace(
        /!\[(.*?)\]\((.*?)(?:\s+(\d+)x(\d+))?\)/g,
        (match, alt, url, width, height) => {
          if (width && height) {
            return `<img src="${url}" alt="${alt}" width="${width}" height="${height}" style="aspect-ratio: ${width}/${height}; max-width: 100%; height: auto;">`;
          }
          return match;
        }
      );

      // Convert markdown to HTML with caching
      let html = markdownToHtml(processedContent);

      // Apply security sanitization
      const effectiveSecurityLevel = validation.valid ? securityLevel : 'basic';
      return contentSecurity.sanitizeHtml(html);
    } catch (error) {
      console.error('Error processing markdown content:', error);
      return '<p class="text-destructive">Error processing content</p>';
    }
  }, [content, securityLevel]);

  // Setup image zoom functionality
  useEffect(() => {
    if (!previewRef.current || !renderedContent) return;
    
    const eventListeners = new Map(); // Track event listeners for proper cleanup
    const imagesWithHandlers = new Set(); // Track images with handlers
    
    // Add click listeners to images for zooming
    const setupImageZoom = () => {
      const images = previewRef.current?.querySelectorAll('img:not(.emoji)');
      if (!images) return;
      
      const handleImageClick = (e: Event, src: string | null) => {
        e.preventDefault();
        if (src) setZoomedImage(src);
      };
      
      images.forEach(img => {
        if (!img.hasAttribute('data-zoom-enabled')) {
          img.setAttribute('data-zoom-enabled', 'true');
          if (img instanceof HTMLElement) {
            img.style.cursor = 'zoom-in';
          }
          
          const src = img.getAttribute('src');
          const clickHandler = (e: Event) => handleImageClick(e, src);
          
          img.addEventListener('click', clickHandler);
          eventListeners.set(img, clickHandler);
          imagesWithHandlers.add(img);
        }
      });
    };
    
    // Defer the setup to avoid blocking the main thread
    const setupTimeout = setTimeout(setupImageZoom, 50);
    
    return () => {
      clearTimeout(setupTimeout);
      
      // Clean up event listeners
      imagesWithHandlers.forEach(img => {
        const handler = eventListeners.get(img);
        if (handler && img instanceof Element) {
          img.removeEventListener('click', handler);
        }
      });
      
      // Clear collections
      eventListeners.clear();
      imagesWithHandlers.clear();
    };
  }, [renderedContent, setZoomedImage]);

  // Use a separate effect for Prism highlighting with proper performance optimization
  useEffect(() => {
    if (!previewRef.current || !renderedContent || isProcessed) return;
    
    // Clean up previous timeout if any
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
      processingTimeoutRef.current = null;
    }
    
    // Process markdown content with progressive rendering
    const cleanup = processMarkdownContent(previewRef.current, {
      // Handle code block highlighting
      onHighlightCode: (codeElement) => {
        try {
          Prism.highlightElement(codeElement);
          
          // Add enhancements to code blocks
          const pre = codeElement.parentElement;
          if (pre && !pre.classList.contains('enhanced')) {
            pre.classList.add('enhanced');
            pre.classList.add('line-numbers');
            
            // Add language label
            const language = codeElement.className.match(/language-(\w+)/)?.[1] || '';
            if (language) {
              const langLabel = document.createElement('div');
              langLabel.className = 'code-language absolute top-2 left-2 px-2 py-1 rounded bg-muted/80 text-xs font-mono';
              langLabel.textContent = language;
              pre.style.position = 'relative';
              pre.appendChild(langLabel);
            }
            
            // Add copy button
            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-button absolute top-2 right-2 p-1.5 rounded bg-muted/80 hover:bg-muted text-xs';
            copyBtn.innerHTML = 'Copy';
            copyBtn.onclick = (e) => {
              e.preventDefault();
              e.stopPropagation();
              navigator.clipboard.writeText(codeElement.textContent || '');
              copyBtn.innerHTML = 'Copied!';
              setTimeout(() => {
                copyBtn.innerHTML = 'Copy';
              }, 2000);
            };
            pre.appendChild(copyBtn);
          }
        } catch (error) {
          console.error('Error highlighting code:', error);
        }
      },
      
      // Handle table enhancements
      onEnhanceTables: (table) => {
        table.classList.add('enhanced-table');

        // Ensure table has proper wrapper for responsive scrolling
        if (!table.parentElement?.classList.contains('table-wrapper')) {
          const wrapper = document.createElement('div');
          wrapper.className = 'table-wrapper';
          table.parentNode?.insertBefore(wrapper, table);
          wrapper.appendChild(table);
        }

        // Add proper styling classes
        table.classList.add('w-full', 'border-collapse');

        // Style table headers
        const headers = table.querySelectorAll('th');
        headers.forEach(th => {
          th.classList.add('bg-muted/80', 'font-semibold', 'text-foreground');
        });

        // Style table cells
        const cells = table.querySelectorAll('td');
        cells.forEach(td => {
          td.classList.add('text-foreground');
        });
      },
      
      // Handle link enhancements
      onEnhanceLinks: (link) => {
        link.classList.add('enhanced-link');
        
        const href = link.getAttribute('href');
        if (href && (href.startsWith('http://') || href.startsWith('https://'))) {
          link.setAttribute('target', '_blank');
          link.setAttribute('rel', 'noopener noreferrer');
          
          // Add external link icon if not already present
          if (!link.querySelector('.external-link-icon')) {
            const iconSpan = document.createElement('span');
            iconSpan.className = 'external-link-icon inline-block ml-1';
            iconSpan.innerHTML = '<svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg>';
            link.appendChild(iconSpan);
          }
        }
      },
      
      // Mark processing as complete
      onComplete: () => {
        setIsProcessed(true);
      }
    });
    
    return cleanup;
  }, [renderedContent, isProcessed]);

  const handleCloseZoom = useCallback(() => {
    setZoomedImage(null);
  }, []);

  return (
    <>
      <div 
        ref={previewRef} 
        className={cn("markdown-preview", className)}
        dangerouslySetInnerHTML={{ __html: renderedContent }}
      />
      
      {zoomedImage && (
        <div 
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={handleCloseZoom}
        >
          <button 
            className="absolute top-4 right-4 bg-white/10 rounded-full p-2"
            onClick={handleCloseZoom}
          >
            <X className="h-6 w-6 text-white" />
          </button>
          <img 
            src={zoomedImage} 
            alt="Zoomed" 
            className="max-h-[85vh] max-w-[85vw] object-contain"
          />
        </div>
      )}
    </>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default React.memo(MarkdownPreview);
