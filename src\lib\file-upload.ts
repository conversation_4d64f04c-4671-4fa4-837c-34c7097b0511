
/**
 * Utility functions for handling file uploads
 */

/**
 * Convert a File object to a base64 data URL
 */
export const fileToDataUrl = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * Get file type category (image, video, other)
 */
export const getFileType = (file: File): 'image' | 'video' | 'other' => {
  if (file.type.startsWith('image/')) return 'image';
  if (file.type.startsWith('video/')) return 'video';
  return 'other';
};

/**
 * Convert video file to embedded URL format if needed
 * For uploaded videos, we use the data URL directly
 */
export const processVideoUrl = (url: string): string => {
  // For data URLs, return as is
  if (url.startsWith('data:')) return url;
  
  // Handle YouTube URLs
  if (url.includes('youtube.com/watch') || url.includes('youtu.be')) {
    const videoId = url.includes('youtu.be') 
      ? url.split('/').pop() 
      : new URL(url).searchParams.get('v');
    
    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}`;
    }
  }
  
  // Handle Vimeo URLs
  if (url.includes('vimeo.com')) {
    const vimeoId = url.split('/').pop();
    if (vimeoId) {
      return `https://player.vimeo.com/video/${vimeoId}`;
    }
  }
  
  return url;
};
