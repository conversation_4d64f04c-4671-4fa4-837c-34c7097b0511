import React, { useEffect, useMemo, useRef, useCallback } from 'react';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { motion } from 'framer-motion';
import '@/styles/unified-lesson-content.css';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LessonContentProps {
  content: string;
  className?: string;
}

interface ParsedContent {
  videoUrl: string | null;
  imageUrl: string | null;
  textContent: string;
  externalRedirectUrl: string | null;
  mainContent: string;
  referencesContent: string;
}

const LessonContent: React.FC<LessonContentProps> = React.memo(({ content, className = '' }) => {
  const isMountedRef = useRef(true);
  const redirectTimerRef = useRef<number | null>(null);

  // Cleanup to prevent memory leaks and race conditions
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;

      // Clear any pending timers
      if (redirectTimerRef.current) {
        clearTimeout(redirectTimerRef.current);
        redirectTimerRef.current = null;
      }
    };
  }, []);
  
  // Simplified content parsing with better error handling
  const parseContent = useCallback((rawContent: string): ParsedContent => {
    if (!rawContent) {
      return {
        videoUrl: null,
        imageUrl: null,
        textContent: '',
        externalRedirectUrl: null,
        mainContent: '',
        referencesContent: ''
      };
    }

    // Initialize with defaults
    let videoUrl: string | null = null;
    let imageUrl: string | null = null;
    let textContent = rawContent;
    let externalRedirectUrl: string | null = null;

    // Try to parse JSON content (for rich media lessons)
    try {
      const parsed = JSON.parse(rawContent);
      if (parsed && typeof parsed === 'object') {
        videoUrl = parsed.videoUrl || null;
        imageUrl = parsed.imageUrl || null;
        textContent = parsed.content || rawContent;
        externalRedirectUrl = parsed.externalRedirectUrl || null;
      }
    } catch {
      // Not JSON, treat as plain markdown
      textContent = rawContent;
    }

    // Split content into main and references sections
    const referencesMatch = textContent.match(/(^|\n)(#+\s*References?\s*\n[\s\S]*)$/i);
    let mainContent = textContent;
    let referencesContent = '';

    if (referencesMatch) {
      const splitIndex = referencesMatch.index! + referencesMatch[1].length;
      mainContent = textContent.slice(0, splitIndex).trim();
      referencesContent = textContent.slice(splitIndex).trim();
    }

    return {
      videoUrl,
      imageUrl,
      textContent,
      externalRedirectUrl,
      mainContent,
      referencesContent
    };
  }, []);

  // Memoize parsed content
  const parsedContent = useMemo(() => parseContent(content), [content, parseContent]);

  // Handle external redirect with proper cleanup
  useEffect(() => {
    const { externalRedirectUrl } = parsedContent;
    if (!externalRedirectUrl) return;

    // Redirect after a short delay to allow the page to render
    redirectTimerRef.current = window.setTimeout(() => {
      if (isMountedRef.current) {
        window.open(externalRedirectUrl, '_blank');
      }
    }, 500);

    return () => {
      if (redirectTimerRef.current) {
        clearTimeout(redirectTimerRef.current);
        redirectTimerRef.current = null;
      }
    };
  }, [parsedContent.externalRedirectUrl]);

  // Early return for empty content
  if (!content) {
    return (
      <div className="lesson-content-container">
        <div className="flex items-center justify-center p-8 bg-muted/30 rounded-lg">
          <p className="text-muted-foreground text-center">No content available for this lesson.</p>
        </div>
      </div>
    );
  }

  const { videoUrl, imageUrl, mainContent, referencesContent, externalRedirectUrl } = parsedContent;

  return (
    <motion.div
      className={`lesson-content-container lesson-content-no-padding ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* External redirect button */}
      {externalRedirectUrl && (
        <motion.div
          className="mb-6 flex flex-col items-center justify-center p-6 bg-primary/5 rounded-xl border border-primary/20"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h2 className="text-xl font-semibold mb-3">External Form</h2>
          <p className="text-center mb-4">
            You are being redirected to an external form. If you are not redirected automatically, please click the button below.
          </p>
          <Button
            size="lg"
            onClick={() => window.open(externalRedirectUrl, '_blank')}
            className="flex items-center"
          >
            <ExternalLink className="mr-2 h-5 w-5" />
            Open External Form
          </Button>
        </motion.div>
      )}

      {/* Video section */}
      {videoUrl && (
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="video-container aspect-video rounded-lg overflow-hidden shadow-md">
            {videoUrl.startsWith('data:video/') ? (
              <video
                controls
                className="w-full h-full"
                autoPlay={false}
                preload="metadata"
              >
                <source src={videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            ) : (
              <iframe
                src={videoUrl}
                title="Lesson video"
                style={{ border: 0 }}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                loading="lazy"
                className="w-full h-full"
              />
            )}
          </div>
        </motion.div>
      )}

      {/* Image section */}
      {imageUrl && (
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <figure className="text-center">
            <img
              src={imageUrl}
              alt="Lesson image"
              loading="lazy"
              className="max-w-full h-auto rounded-lg shadow-md mx-auto"
            />
          </figure>
        </motion.div>
      )}

      {/* Main content section */}
      <motion.div
        className="lesson-prose"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <MarkdownPreview
          content={mainContent}
          className="lesson-prose"
          allowHtml={true}
          securityLevel="extended"
        />
      </motion.div>

      {/* References section */}
      {referencesContent && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-8"
        >
          <Accordion type="single" collapsible className="lesson-accordion">
            <AccordionItem value="references" className="lesson-accordion-item">
              <AccordionTrigger className="lesson-accordion-trigger">
                <span className="flex items-center gap-3 text-lg font-semibold">
                  📚 References & Additional Resources
                </span>
              </AccordionTrigger>
              <AccordionContent className="lesson-accordion-content">
                <div className="lesson-prose">
                  <MarkdownPreview
                    content={referencesContent}
                    className="lesson-prose"
                    allowHtml={true}
                    securityLevel="extended"
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </motion.div>
      )}
    </motion.div>
  );
});

LessonContent.displayName = 'LessonContent';

export default LessonContent;
